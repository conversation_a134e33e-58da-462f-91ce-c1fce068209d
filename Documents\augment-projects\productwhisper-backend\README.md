# ProductWhisper Backend

A production-ready Node.js/TypeScript backend for ProductWhisper - a platform that helps users discover products through sentiment analysis of reviews and discussions from various sources. This backend provides public APIs without authentication requirements.

## Technology Stack

- **Fastify + TypeScript**: High-performance API server
- **Prisma ORM**: Type-safe database operations
- **PostgreSQL 15+**: Optimized database schema
- **Redis 7+**: Multi-layer caching
- **Zod**: Runtime type validation
- **Winston**: Structured logging
- **Socket.io**: Real-time features
- **FastAPI**: Python sentiment analysis service
- **Docker**: Containerized deployment

## Project Structure

```
productwhisper-backend/
├── src/
│   ├── api/
│   │   ├── controllers/      # Request handlers
│   │   ├── middleware/       # Fastify middleware
│   │   ├── routes/          # API route definitions
│   │   ├── validators/      # Request validation schemas
│   │   └── plugins/         # Fastify plugins setup
│   ├── core/
│   │   ├── entities/        # Domain entities
│   │   ├── repositories/    # Data access layer
│   │   ├── services/        # Business logic
│   │   └── use-cases/       # Application use cases
│   ├── infrastructure/
│   │   ├── database/        # Database configuration
│   │   ├── cache/          # Redis cache implementation
│   │   ├── external-apis/   # External API integrations
│   │   ├── messaging/       # Message queue setup
│   │   └── monitoring/      # Metrics and monitoring
│   ├── shared/
│   │   ├── constants/       # Application constants
│   │   ├── types/          # TypeScript type definitions
│   │   ├── utils/          # Utility functions
│   │   └── errors/         # Error handling
│   ├── config/             # Configuration management
│   └── tests/              # Test utilities
├── python-services/
│   └── sentiment-analysis/  # FastAPI sentiment service
├── tests/                   # Test files
├── scripts/                 # Database and deployment scripts
├── docs/                    # Documentation
└── docker/                  # Docker configurations
```

## Getting Started

### Prerequisites

- **Node.js** (v18+)
- **PostgreSQL** (v15+)
- **Redis** (v7+)
- **Python** (v3.11+)
- **Docker** (optional, for containerized development)

### Quick Start with Docker

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd productwhisper-backend
   ```

2. **Copy environment file**
   ```bash
   cp .env.example .env
   ```

3. **Start all services with Docker**
   ```bash
   docker-compose up -d
   ```

4. **Check service health**
   ```bash
   curl http://localhost:8000/health
   ```

### Manual Installation

1. **Install dependencies**
   ```bash
   npm install
   ```

2. **Set up environment variables**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

3. **Start PostgreSQL and Redis**
   ```bash
   # Using Docker
   docker run -d --name postgres -p 5432:5432 -e POSTGRES_PASSWORD=productwhisper123 postgres:15-alpine
   docker run -d --name redis -p 6379:6379 redis:7-alpine
   ```

4. **Generate Prisma client and run migrations**
   ```bash
   npm run db:generate
   npm run db:migrate
   ```

5. **Start the development server**
   ```bash
   npm run dev
   ```

### Database Setup

There are two ways to set up the database:

#### Option 1: Using the initialization script

This will create the database and run the schema.sql script:

```bash
npm run db:init
```

#### Option 2: Using migrations (recommended)

This approach uses node-pg-migrate to manage database schema changes:

```bash
# Create the database first
createdb productwhisper

# Run all migrations
npm run migrate:up
```

Migrations provide better version control and allow for incremental schema changes. See the [migrations README](./migrations/README.md) for more details.

### Running the Application

1. Start the Node.js server:
   ```
   npm run dev
   ```
2. Start the Python sentiment analysis service:
   ```
   cd python-nlp-service
   python app.py
   ```

## API Endpoints

### Authentication

- `POST /api/auth/register` - Register a new user
- `POST /api/auth/login` - Login and get JWT
- `POST /api/auth/refresh` - Refresh JWT token
- `GET /api/auth/me` - Get current user info

### Products

- `GET /api/products` - Search products with filters
- `GET /api/products/:id` - Get product details with sentiment analysis
- `GET /api/products/:id/mentions` - Get product mentions/reviews

### Search

- `POST /api/search` - Perform a new product search
- `GET /api/search/recent` - Get recent searches

### User

- `GET /api/user/favorites` - Get user favorites
- `POST /api/user/favorites` - Add product to favorites
- `DELETE /api/user/favorites/:id` - Remove from favorites
- `PUT /api/user/preferences` - Update user preferences

## License

This project is licensed under the MIT License.
