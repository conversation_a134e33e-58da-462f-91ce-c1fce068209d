{"name": "productwhisper-backend", "version": "1.0.0", "description": "Production-ready Node.js/TypeScript backend for ProductWhisper", "main": "dist/server.js", "scripts": {"dev": "tsx watch src/server.ts", "build": "tsc && tsc-alias", "start": "node dist/server.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:e2e": "jest --config jest.e2e.config.js", "db:migrate": "prisma migrate dev", "db:generate": "prisma generate", "db:seed": "tsx src/scripts/seed.ts", "db:studio": "prisma studio", "db:reset": "prisma migrate reset", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "format": "prettier --write src/**/*.ts", "docker:build": "docker-compose build", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "docker:logs": "docker-compose logs -f"}, "keywords": ["nodejs", "typescript", "fastify", "prisma", "postgresql", "redis", "sentiment-analysis", "api"], "author": "ProductWhisper Team", "license": "MIT", "dependencies": {"@fastify/cors": "^9.0.1", "@fastify/helmet": "^11.1.1", "@fastify/rate-limit": "^9.1.0", "@fastify/redis": "^6.1.1", "@fastify/swagger": "^8.14.0", "@fastify/swagger-ui": "^2.1.0", "@fastify/websocket": "^10.0.1", "@prisma/client": "^5.7.1", "fastify": "^4.25.2", "fastify-plugin": "^4.5.1", "ioredis": "^5.3.2", "socket.io": "^4.7.4", "winston": "^3.11.0", "zod": "^3.22.4", "axios": "^1.6.2", "dotenv": "^16.3.1", "nanoid": "^5.0.4", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/jest": "^29.5.8", "@types/jsonwebtoken": "^9.0.5", "@types/node": "^20.10.4", "@types/supertest": "^6.0.2", "@typescript-eslint/eslint-plugin": "^6.13.1", "@typescript-eslint/parser": "^6.13.1", "eslint": "^8.54.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.1", "husky": "^8.0.3", "jest": "^29.7.0", "lint-staged": "^15.2.0", "prettier": "^3.1.0", "prisma": "^5.7.1", "supertest": "^6.3.3", "testcontainers": "^10.4.0", "ts-jest": "^29.1.1", "tsc-alias": "^1.8.8", "tsx": "^4.6.2", "typescript": "^5.3.3"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "lint-staged": {"*.{ts,js}": ["eslint --fix", "prettier --write"]}}