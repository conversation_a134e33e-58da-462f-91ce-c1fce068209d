{"compilerOptions": {"target": "ES2022", "module": "commonjs", "lib": ["ES2022"], "outDir": "./dist", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "declaration": true, "declarationMap": true, "sourceMap": true, "removeComments": true, "noImplicitAny": true, "noImplicitReturns": true, "noImplicitThis": true, "noUnusedLocals": true, "noUnusedParameters": true, "exactOptionalPropertyTypes": true, "noImplicitOverride": true, "noPropertyAccessFromIndexSignature": true, "noUncheckedIndexedAccess": true, "allowUnusedLabels": false, "allowUnreachableCode": false, "experimentalDecorators": true, "emitDecoratorMetadata": true, "baseUrl": "./src", "paths": {"@/*": ["*"], "@/api/*": ["api/*"], "@/core/*": ["core/*"], "@/infrastructure/*": ["infrastructure/*"], "@/shared/*": ["shared/*"], "@/config/*": ["config/*"], "@/tests/*": ["../tests/*"]}}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "python-services"], "ts-node": {"require": ["tsconfig-paths/register"]}}