version: '3.8'

services:
  # Backend API service
  api:
    build:
      context: ./backend
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - PORT=3000
      - DB_HOST=postgres
      - DB_PORT=5432
      - DB_NAME=productwhisper
      - DB_USER=postgres
      - DB_PASSWORD=${DB_PASSWORD}
      - REDIS_URL=redis://redis:6379
      - SENTIMENT_API_URL=http://nlp-service:5000
    depends_on:
      - postgres
      - redis
      - nlp-service
    restart: unless-stopped
    volumes:
      - api-logs:/app/logs
    networks:
      - app-network

  # Python NLP service
  nlp-service:
    build:
      context: ./backend/python-nlp-service
      dockerfile: Dockerfile
    ports:
      - "5000:5000"
    restart: unless-stopped
    networks:
      - app-network

  # Frontend service
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
      args:
        - REACT_APP_API_URL=http://localhost:3000/api
    ports:
      - "80:80"
    depends_on:
      - api
    restart: unless-stopped
    networks:
      - app-network

  # PostgreSQL database
  postgres:
    image: postgres:14-alpine
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_DB=productwhisper
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=${DB_PASSWORD}
    volumes:
      - postgres-data:/var/lib/postgresql/data
    restart: unless-stopped
    networks:
      - app-network

  # Redis cache
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    restart: unless-stopped
    networks:
      - app-network

  # Prometheus monitoring
  prometheus:
    image: prom/prometheus:latest
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus-data:/prometheus
    restart: unless-stopped
    networks:
      - app-network

  # Grafana dashboard
  grafana:
    image: grafana/grafana:latest
    ports:
      - "3001:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD}
    volumes:
      - grafana-data:/var/lib/grafana
    depends_on:
      - prometheus
    restart: unless-stopped
    networks:
      - app-network

volumes:
  postgres-data:
  redis-data:
  api-logs:
  prometheus-data:
  grafana-data:

networks:
  app-network:
    driver: bridge
